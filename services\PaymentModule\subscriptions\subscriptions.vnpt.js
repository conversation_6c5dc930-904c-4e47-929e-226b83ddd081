"use strict";
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const {INPUT_TYPE} = require("../../../constants/constant");
module.exports = {
  actions: {
    check: {
      rest: "GET /check",
      async handler(ctx) {
        const {packageId} = ctx.params;
        const userId = ctx.params.userId || ctx.meta?.user?._id;

        if (!userId) {
          throw new Error("User ID is missing from the request context.");
        }

        const customer = await this.broker.call("customers.getOneByUser", {userId});

        if (!customer) {
          throw new Error(`No customer found for user ID: ${userId}`);
        }

        const activeSubscriptions = await this.broker.call("subscriptions.find", {
          query: {
            customerId: customer._id,
            packageId,
            status: "ACTIVE"
          }
        });

        const hasActiveSubscription = activeSubscriptions.length > 0;

        return hasActiveSubscription ? {
          state: "exist",
          message: i18next.t("you_have_active_subscription")
        } : {
          state: "not_exist"
        };
      }
    },

    orderFromVNPT: {
      rest: "POST /order/vnpt",
      async handler(ctx) {
        const {packageInfo, discountIds, priceIndex, paymentGateway = "vnpt", user} = ctx.params;
        const userId = ctx.params.user._id || ctx.meta?.user?._id;
        const packageId = packageInfo._id;
        const [customer, promotion] = await Promise.all([
          this.broker.call("customers.getOneByUser", {userId}),
          this.broker.call("promotions.getOne", {packageId, priceIndex})
        ]);
        const price = packageInfo.prices[0];
        console.log("price", price);
        const subscription = await this.createVNPTSubscription(customer._id, packageId, price, "ACTIVE");
        const transaction = await this.insertVNPTTransaction(subscription._id, packageId, customer._id, discountIds, promotion, 0, price,);

        await this.broker.emit("vnptTransactionUpdateState", {
          transactionId: transaction._id.toString(),
          state: "done",
          responseMessage: 'Confirm Success',
        });
        return transaction
      }
    },

  },
  methods: {
    extractIdFromList(list) {
      return list.map(item => item._id);
    },

    async createVNPTSubscription(customerId, packageId, price, status = "INACTIVE") {
      let startDate = new Date();
      let endDate = new Date();


      const activeSubscriptions = await this.adapter.find({
        query: {
          customerId,
          packageId,
          status: 'ACTIVE'
        }
      });
      let currentEndDate = new Date()
      if (activeSubscriptions.length > 0) {
        // Find the subscription with the latest endDate
        const latestSubscription = activeSubscriptions.reduce((latest, subscription) =>
          subscription.endDate > latest.endDate ? subscription : latest, activeSubscriptions[0]
        );
        // Set startDate to one day after the latest endDate
        currentEndDate = new Date(latestSubscription.endDate.getTime());
      }

      if (price.unitName === "month") {
        endDate.setMonth(currentEndDate.getMonth() + Number(price.intervalCount));
      } else if (price.unitName === "year") {
        endDate.setFullYear(currentEndDate.getFullYear() + Number(price.intervalCount));
      } else if (price.unitName === "day") {
        endDate.setDate(currentEndDate.getDate() + Number(price.intervalCount));
      }

      // Insert the new subscription
      return this.adapter.insert({
        customerId,
        packageId,
        status,
        isFree: false,
        startDate,
        endDate,
        autoRenew: true,
        isVNPTConsumer: true,
        unitPrice: price.unitName,
        intervalCount: price.intervalCount
      });
    },

    async insertVNPTTransaction(subscriptionId, packageId, customerId, discountIds, promotion, cost, price, paymentMethod = "vnpt", quantity = 1) {
      return this.broker.call("transactions.insert", {
        entity: {
          subscriptionId,
          discountIds,
          customerId,
          promotionId: promotion?._id,
          packageId,
          content: "",
          paymentCode: "",
          paymentUnit: "",
          packageQuantity: quantity,
          unitPrice: price.unitName,
          intervalCount: price.intervalCount,
          cost,
          paymentMethod: paymentMethod,
          state: paymentMethod === "vnpt" ? "done" : "processing",
        }
      });
    },
    async vnptPaymentDone(subscription, customer, user) {
      await this.broker.emit("vnptSubscriptionActive", {subscription, customer, user});
      const packageFree = await this.broker.call("packages.find", {
        query: {
          paidType: "free", type: 'base', customerTarget: "student"
        }
      });
      await this.adapter.updateMany(
        {customerId: subscription.customerId, packageId: packageFree[0]?._id, status: "ACTIVE"},
        {status: "INACTIVE"}
      );
    },
  },
  events: {


    async customerCreatedFromPhone({customer, packageId}) {

      const entity = {
        customerId: customer._id,
        packageId,
        startDate: new Date(),
        endDate: new Date(new Date().getTime() + 30 * 24 * 60 * 60 * 1000),
        status: 'ACTIVE',
      };
      const packageInfo = await this.broker.call("packages.get", {id: packageId});
      const subscription = await this.adapter.insert(entity);
      await this.broker.emit("subscriptionCreated", {subscription, customerTarget: packageInfo.customerTarget});
    },
    vnptSubscriptionUpdateStatus: {
      params: {
        subscriptionId: "string",
      },
      async handler(ctx) {
        const {subscriptionId, customer} = ctx.params;
        const subscription = await this.adapter.updateById(subscriptionId, {status: "ACTIVE"});
        const user = await ctx.call("users.get", {id: customer.userId.toString()});
        return this.vnptPaymentDone(subscription, customer, user);
      }
    }

  },
};
