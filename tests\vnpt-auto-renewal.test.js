const { ServiceBroker } = require("moleculer");
const TestService = require("moleculer-db");

describe("VNPT Auto Renewal Cronjob Tests", () => {
  let broker;
  let subscriptionService;

  beforeAll(async () => {
    broker = new ServiceBroker({ logger: false });
    
    // Load subscription service
    subscriptionService = broker.loadService("./services/PaymentModule/subscriptions/subscriptions.service.js");
    
    await broker.start();
  });

  afterAll(async () => {
    await broker.stop();
  });

  describe("calculateNewEndDate method", () => {
    test("should calculate correct end date for monthly subscription", () => {
      const currentEndDate = new Date('2024-01-15');
      const unitPrice = 'month';
      const intervalCount = 1;
      
      const result = subscriptionService.calculateNewEndDate(currentEndDate, unitPrice, intervalCount);
      
      expect(result.getMonth()).toBe(1); // February (0-indexed)
      expect(result.getDate()).toBe(15);
    });

    test("should calculate correct end date for yearly subscription", () => {
      const currentEndDate = new Date('2024-01-15');
      const unitPrice = 'year';
      const intervalCount = 1;
      
      const result = subscriptionService.calculateNewEndDate(currentEndDate, unitPrice, intervalCount);
      
      expect(result.getFullYear()).toBe(2025);
      expect(result.getMonth()).toBe(0); // January
      expect(result.getDate()).toBe(15);
    });

    test("should calculate correct end date for daily subscription", () => {
      const currentEndDate = new Date('2024-01-15');
      const unitPrice = 'day';
      const intervalCount = 30;
      
      const result = subscriptionService.calculateNewEndDate(currentEndDate, unitPrice, intervalCount);
      
      expect(result.getDate()).toBe(14); // 15 + 30 = 45, which wraps to next month
      expect(result.getMonth()).toBe(1); // February
    });

    test("should handle multiple intervals", () => {
      const currentEndDate = new Date('2024-01-15');
      const unitPrice = 'month';
      const intervalCount = 3;
      
      const result = subscriptionService.calculateNewEndDate(currentEndDate, unitPrice, intervalCount);
      
      expect(result.getMonth()).toBe(3); // April (0-indexed)
      expect(result.getDate()).toBe(15);
    });
  });

  describe("autoRenewVNPTSubscriptions method", () => {
    test("should return success result with counts", async () => {
      // Mock adapter.find to return test data
      const mockSubscriptions = [
        {
          _id: "507f1f77bcf86cd799439011",
          customerId: "507f1f77bcf86cd799439012",
          endDate: new Date('2024-01-01'),
          unitPrice: 'month',
          intervalCount: 1,
          autoRenew: true,
          isVNPTConsumer: true,
          status: 'ACTIVE'
        }
      ];

      subscriptionService.adapter.find = jest.fn().mockResolvedValue(mockSubscriptions);
      subscriptionService.adapter.updateById = jest.fn().mockResolvedValue({});
      subscriptionService.resetPermissionsUsage = jest.fn().mockResolvedValue();

      const result = await subscriptionService.autoRenewVNPTSubscriptions();

      expect(result.success).toBe(true);
      expect(result.renewedCount).toBe(1);
      expect(result.errorCount).toBe(0);
      expect(result.errors).toHaveLength(0);
    });

    test("should handle errors gracefully", async () => {
      const mockSubscriptions = [
        {
          _id: "507f1f77bcf86cd799439011",
          customerId: "507f1f77bcf86cd799439012",
          endDate: new Date('2024-01-01'),
          unitPrice: 'month',
          intervalCount: 1,
          autoRenew: true,
          isVNPTConsumer: true,
          status: 'ACTIVE'
        }
      ];

      subscriptionService.adapter.find = jest.fn().mockResolvedValue(mockSubscriptions);
      subscriptionService.adapter.updateById = jest.fn().mockRejectedValue(new Error('Database error'));
      subscriptionService.resetPermissionsUsage = jest.fn().mockResolvedValue();

      const result = await subscriptionService.autoRenewVNPTSubscriptions();

      expect(result.success).toBe(true);
      expect(result.renewedCount).toBe(0);
      expect(result.errorCount).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('Database error');
    });
  });

  describe("resetPermissionsUsage method", () => {
    test("should reset all usage counters to 0", async () => {
      const customerId = "507f1f77bcf86cd799439012";
      const subscriptionId = "507f1f77bcf86cd799439011";

      const mockCustomer = {
        _id: customerId,
        userId: "507f1f77bcf86cd799439013"
      };

      const mockPermissions = [
        {
          _id: "507f1f77bcf86cd799439014",
          userId: mockCustomer.userId,
          subscriptionId: subscriptionId.toString(),
          accessLimit: {
            textUsed: 10,
            mediaUsed: 5,
            capacityUsed: 100,
            speakingUsed: 3,
            writingUsed: 2,
            dictationUsed: 1,
            shadowingUsed: 1,
            speakingRoomUsed: 1
          }
        }
      ];

      subscriptionService.broker.call = jest.fn()
        .mockResolvedValueOnce(mockCustomer) // customers.get
        .mockResolvedValueOnce(mockPermissions) // permissions.find
        .mockResolvedValue({}); // permissions.update

      await subscriptionService.resetPermissionsUsage(customerId, subscriptionId);

      // Verify permissions.update was called with reset values
      expect(subscriptionService.broker.call).toHaveBeenCalledWith("permissions.update", {
        id: mockPermissions[0]._id,
        accessLimit: expect.objectContaining({
          textUsed: 0,
          mediaUsed: 0,
          capacityUsed: 0,
          speakingUsed: 0,
          writingUsed: 0,
          dictationUsed: 0,
          shadowingUsed: 0,
          speakingRoomUsed: 0
        })
      });
    });

    test("should throw error if customer has no userId", async () => {
      const customerId = "507f1f77bcf86cd799439012";
      const subscriptionId = "507f1f77bcf86cd799439011";

      const mockCustomer = {
        _id: customerId,
        userId: null
      };

      subscriptionService.broker.call = jest.fn().mockResolvedValue(mockCustomer);

      await expect(subscriptionService.resetPermissionsUsage(customerId, subscriptionId))
        .rejects.toThrow(`Customer ${customerId} không có userId`);
    });
  });

  describe("Integration test", () => {
    test("should process expired VNPT subscriptions end-to-end", async () => {
      // This would be a more comprehensive test that sets up real test data
      // and verifies the entire flow works correctly
      
      const result = await broker.call("subscriptions.testVNPTAutoRenewal");
      
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('testSubscriptions');
      expect(result).toHaveProperty('result');
      expect(result.result).toHaveProperty('success');
      expect(result.result).toHaveProperty('renewedCount');
      expect(result.result).toHaveProperty('errorCount');
      expect(result.result).toHaveProperty('errors');
    });
  });
});
