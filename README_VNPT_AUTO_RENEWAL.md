# VNPT Subscription Auto Renewal System

## Tổng quan

Hệ thống tự động gia hạn subscription cho người dùng VNPT được thiết kế để tự động gia hạn các subscription đã hết hạn cho người dùng VNPT có bật tính năng auto renewal.

## Tính năng chính

### 🔄 Auto Renewal Cronjob
- **Thời gian chạy**: Hàng ngày lúc 00:00 (có thể tùy chỉnh)
- **Điều kiện gia hạn**: 
  - `endDate` <= thời điểm hiện tại
  - `autoRenew: true`
  - `isVNPTConsumer: true`
  - `status: 'ACTIVE'`
  - `isDeleted: false`

### 📊 Reset Usage Permissions
- Tự động reset các usage counters sau khi gia hạn thành công
- Bao gồm: textUsed, mediaUsed, capacityUsed, speakingUsed, writingUsed, dictationUsed, shadowingUsed, speakingRoomUsed

### 📱 Telegram Notifications
- Thông báo tự động qua Telegram khi gia hạn thành công
- Chi tiết thông tin subscription và thời gian gia hạn

### 📝 Comprehensive Logging
- Log chi tiết quá trình gia hạn
- Error handling và reporting
- Metrics tracking

## Cài đặt và Cấu hình

### 1. Files đã được tạo/cập nhật

```
services/cronjobs/cron.service.js                    # Cấu hình cronjob
services/PaymentModule/subscriptions/subscriptions.service.js  # Logic gia hạn
services/bot/telegram.service.js                     # Telegram notifications
docs/VNPT_AUTO_RENEWAL_CRONJOB.md                   # Documentation chi tiết
tests/vnpt-auto-renewal.test.js                     # Unit tests
scripts/test-vnpt-auto-renewal.js                   # Test script
```

### 2. Cronjob Configuration

Cronjob được cấu hình trong `services/cronjobs/cron.service.js`:

```javascript
{
  name: "vnpt-subscription-auto-renewal",
  cronTime: '0 0 * * *', // Chạy hàng ngày lúc 00:00
  async onTick() {
    await this.broker.call('subscriptions.autoRenewVNPTSubscriptions');
  }
}
```

### 3. Telegram Configuration

Đảm bảo đã cấu hình Telegram Bot trong settings:
- `telegramBotToken`: Token của Telegram Bot
- `telegramChatId`: Chat ID để nhận thông báo

## Sử dụng

### 1. Chạy Auto Renewal thủ công

```bash
# Gọi API test
curl -X POST http://localhost:3000/api/subscriptions/testVNPTAutoRenewal

# Hoặc sử dụng test script
node scripts/test-vnpt-auto-renewal.js
```

### 2. Kiểm tra Logs

```bash
# Xem logs của cronjob service
tail -f logs/cronjobs.log

# Xem logs của subscription service  
tail -f logs/subscriptions.log
```

### 3. Monitoring

Kiểm tra các metrics sau:
- `renewedCount`: Số subscription được gia hạn thành công
- `errorCount`: Số subscription gặp lỗi
- `errors[]`: Chi tiết các lỗi

## API Endpoints

### 1. Auto Renewal Action
```
Service: subscriptions.autoRenewVNPTSubscriptions
Description: Action chính được gọi từ cronjob
```

### 2. Test Action
```
POST /api/subscriptions/testVNPTAutoRenewal
Description: Test action để kiểm tra hoạt động
Response: {
  "message": "Test VNPT auto renewal completed",
  "testSubscriptions": 5,
  "result": {
    "success": true,
    "renewedCount": 3,
    "errorCount": 0,
    "errors": []
  }
}
```

## Testing

### 1. Unit Tests

```bash
# Chạy unit tests
npm test tests/vnpt-auto-renewal.test.js
```

### 2. Integration Test

```bash
# Chạy test script
node scripts/test-vnpt-auto-renewal.js
```

### 3. Manual Testing

1. Tạo subscription VNPT test với `endDate` trong quá khứ
2. Đặt `autoRenew: true` và `isVNPTConsumer: true`
3. Chạy test API hoặc script
4. Kiểm tra subscription đã được gia hạn
5. Kiểm tra permissions đã được reset
6. Kiểm tra thông báo Telegram

## Troubleshooting

### Lỗi thường gặp

1. **Customer không có userId**
   ```
   Lỗi: Customer 507f1f77bcf86cd799439012 không có userId
   Khắc phục: Kiểm tra data integrity trong bảng customers
   ```

2. **Permission không tìm thấy**
   ```
   Lỗi: Không tìm thấy permissions cho user
   Khắc phục: Kiểm tra liên kết giữa subscription và permissions
   ```

3. **Lỗi tính toán ngày**
   ```
   Lỗi: Invalid date calculation
   Khắc phục: Kiểm tra giá trị unitPrice và intervalCount
   ```

### Debug Steps

1. Kiểm tra logs trong console
2. Sử dụng test API để debug từng subscription
3. Kiểm tra database consistency
4. Restart cronjob service nếu cần

## Performance Considerations

- Xử lý từng subscription tuần tự để tránh overload
- Không sử dụng transaction để tránh lock database
- Log chi tiết để dễ monitor và debug
- Error handling không ảnh hưởng đến các subscription khác

## Security

- Chỉ xử lý subscription có `isVNPTConsumer: true`
- Kiểm tra điều kiện `autoRenew: true` trước khi gia hạn
- Validate dữ liệu trước khi cập nhật
- Log tất cả hoạt động để audit

## Monitoring và Alerting

### Metrics cần theo dõi
- Số subscription được gia hạn hàng ngày
- Tỷ lệ lỗi trong quá trình gia hạn
- Thời gian xử lý cronjob
- Số lượng subscription VNPT active

### Alerts
- Telegram notification khi gia hạn thành công
- Error alerts khi có lỗi trong quá trình gia hạn
- Daily summary report

## Maintenance

### Định kỳ kiểm tra
- Kiểm tra logs hàng ngày
- Monitor database performance
- Verify subscription data integrity
- Update documentation khi có thay đổi

### Backup và Recovery
- Backup database trước khi chạy cronjob
- Có plan rollback nếu cần thiết
- Test recovery procedures định kỳ

## Support

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs chi tiết
2. Chạy test script để debug
3. Liên hệ team development với thông tin lỗi cụ thể
