#!/usr/bin/env node

/**
 * Script để test cronjob VNPT auto renewal
 * Sử dụng: node scripts/test-vnpt-auto-renewal.js
 */

const { ServiceBroker } = require("moleculer");

async function testVNPTAutoRenewal() {
  const broker = new ServiceBroker({
    logger: {
      type: "Console",
      options: {
        level: "info",
        colors: true,
        moduleColors: false,
        formatter: "full",
        objectPrinter: null,
        autoPadding: false
      }
    }
  });

  try {
    console.log("🚀 Bắt đầu test VNPT Auto Renewal Cronjob...\n");

    // Load các services cần thiết
    broker.loadService("./services/PaymentModule/subscriptions/subscriptions.service.js");
    broker.loadService("./services/PaymentModule/customers/customers.service.js");
    broker.loadService("./services/permissions/permissions.service.js");
    broker.loadService("./services/bot/telegram.service.js");

    await broker.start();

    console.log("✅ Broker đã khởi động thành công\n");

    // 1. Kiểm tra số lượng subscription VNPT hiện tại
    console.log("📊 Kiểm tra subscription VNPT hiện tại...");
    const vnptSubscriptions = await broker.call("subscriptions.find", {
      query: {
        isVNPTConsumer: true,
        autoRenew: true,
        status: 'ACTIVE',
        isDeleted: false
      }
    });

    console.log(`   Tổng số subscription VNPT có autoRenew: ${vnptSubscriptions.length}`);

    // 2. Kiểm tra subscription đã hết hạn
    const expiredSubscriptions = await broker.call("subscriptions.find", {
      query: {
        endDate: { $lte: new Date() },
        isVNPTConsumer: true,
        autoRenew: true,
        status: 'ACTIVE',
        isDeleted: false
      }
    });

    console.log(`   Số subscription VNPT đã hết hạn: ${expiredSubscriptions.length}\n`);

    if (expiredSubscriptions.length > 0) {
      console.log("📋 Chi tiết subscription đã hết hạn:");
      expiredSubscriptions.forEach((sub, index) => {
        console.log(`   ${index + 1}. ID: ${sub._id}`);
        console.log(`      Customer: ${sub.customerId}`);
        console.log(`      End Date: ${sub.endDate}`);
        console.log(`      Unit Price: ${sub.unitPrice}`);
        console.log(`      Interval Count: ${sub.intervalCount}\n`);
      });
    }

    // 3. Chạy test auto renewal
    console.log("🔄 Chạy test auto renewal...");
    const result = await broker.call("subscriptions.testVNPTAutoRenewal");

    console.log("📊 Kết quả test:");
    console.log(`   Message: ${result.message}`);
    console.log(`   Test Subscriptions: ${result.testSubscriptions}`);
    console.log(`   Renewed Count: ${result.result.renewedCount}`);
    console.log(`   Error Count: ${result.result.errorCount}`);
    
    if (result.result.errors.length > 0) {
      console.log("❌ Errors:");
      result.result.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    // 4. Kiểm tra lại sau khi chạy
    console.log("\n🔍 Kiểm tra lại sau khi chạy auto renewal...");
    const expiredAfter = await broker.call("subscriptions.find", {
      query: {
        endDate: { $lte: new Date() },
        isVNPTConsumer: true,
        autoRenew: true,
        status: 'ACTIVE',
        isDeleted: false
      }
    });

    console.log(`   Số subscription VNPT còn hết hạn: ${expiredAfter.length}`);

    if (result.result.renewedCount > 0) {
      console.log("\n✅ Test hoàn thành thành công!");
      console.log(`   Đã gia hạn ${result.result.renewedCount} subscription(s)`);
    } else if (expiredSubscriptions.length === 0) {
      console.log("\n✅ Test hoàn thành - Không có subscription nào cần gia hạn");
    } else {
      console.log("\n⚠️  Test hoàn thành nhưng có vấn đề - Kiểm tra logs để biết thêm chi tiết");
    }

  } catch (error) {
    console.error("❌ Lỗi trong quá trình test:", error);
  } finally {
    await broker.stop();
    console.log("\n🛑 Broker đã dừng");
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  testVNPTAutoRenewal().catch(console.error);
}

module.exports = testVNPTAutoRenewal;
